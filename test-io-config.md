# Test I/O Configuration Feature

## Tính năng đã triển khai

### 1. Database Schema
- ✅ Bảng `unit_io_config`: <PERSON><PERSON><PERSON> trữ số lượng input/output cho mỗi unit
- ✅ Bảng `ac_output_config`: <PERSON><PERSON><PERSON> hình chi tiết cho AC outputs
- ✅ Bảng `light_output_config`: <PERSON><PERSON><PERSON> hình chi tiết cho Light outputs

### 2. Backend API
- ✅ IPC handlers cho tất cả CRUD operations
- ✅ Database service methods
- ✅ Preload.js expose APIs

### 3. Frontend Components
- ✅ `IOConfigDialog`: Dialog chính cho I/O configuration
- ✅ `ACOutputConfig`: Component cấu hình AC output
- ✅ `LightOutputConfig`: Component cấu hình Light output
- ✅ Tích hợp vào Unit Dialog

### 4. Tính năng chính

#### AC Output Configuration
- Enable/disable AC output
- Windows mode (Off/Save energy)
- Fan type (On/Off/Analog)
- Temperature type (Thermostat/RCU)
- Temperature unit (°C/°F)
- Valve settings (Contact, Type)
- Dead band configuration
- Group assignments cho:
  - Fan controls (Low/Med/High/Analog)
  - Cooling/Heating controls (Analog Cool/Heat, Cool/Heat Open/Close)

#### Light Output Configuration
- Group assignment (0-255)
- Delay timing (On/Off với hours/minutes/seconds)
- Dimming levels (Min/Max percentage)
- Auto trigger với scheduling
- Schedule On/Off timing

### 5. UI Features
- Collapsible cards cho mỗi output
- Tabs để chuyển đổi giữa Lighting và AC outputs
- Real-time validation
- Default values dựa trên unit type
- Responsive design

## Cách test

1. **Tạo hoặc edit một unit**
2. **Mở I/O Config dialog** từ unit dialog (chỉ hiện khi edit unit)
3. **Cấu hình I/O counts** trong phần đầu
4. **Test AC Output Configuration:**
   - Enable AC output
   - Thay đổi các settings
   - Assign groups
   - Save và reload để kiểm tra persistence
5. **Test Light Output Configuration:**
   - Assign group
   - Set delay timing
   - Configure dimming
   - Enable auto trigger và set schedule
   - Save và reload để kiểm tra persistence

## Tương thích với WinForms gốc

Tính năng này được thiết kế dựa trên:
- `AC_Out_Cfg.cs` và `AC_Out_Cfg.Designer.cs`
- `Light_Out_Cfg.cs` và `Light_Out_Cfg.Designer.cs`
- Cấu trúc dữ liệu `ac_out_cfg_t` và `output_info2_t`
- Logic validation và constraints từ ứng dụng gốc

## Các cải tiến so với WinForms

1. **Modern UI**: Sử dụng collapsible cards thay vì separate windows
2. **Better UX**: Tabs để organize outputs, real-time feedback
3. **Validation**: Built-in validation với error messages
4. **Responsive**: Adaptive layout cho different screen sizes
5. **Persistence**: Automatic save/load từ database
