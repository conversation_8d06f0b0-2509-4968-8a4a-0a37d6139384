import React, { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import { ChevronDown, ChevronRight } from "lucide-react";

const ACOutputConfig = ({ outputIndex, config, onChange }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [localConfig, setLocalConfig] = useState({
    enable: false,
    windows_mode: 0,
    fan_type: 0,
    temp_type: 0,
    temp_unit: 0,
    valve_contact: 0,
    valve_type: 0,
    dead_band: 0,
    group_low_fan: 0,
    group_med_fan: 0,
    group_high_fan: 0,
    group_analog_fan: 0,
    group_analog_cool: 0,
    group_analog_heat: 0,
    group_cool_open: 0,
    group_cool_close: 0,
    group_heat_open: 0,
    group_heat_close: 0,
    windows: 0,
    ...config,
  });

  useEffect(() => {
    setLocalConfig((prev) => ({ ...prev, ...config }));
  }, [config]);

  const handleChange = (field, value) => {
    const newConfig = { ...localConfig, [field]: value };
    setLocalConfig(newConfig);
    onChange(newConfig);
  };

  const windowsModeOptions = [
    { value: 0, label: "Off" },
    { value: 1, label: "Save energy" },
  ];

  const fanTypeOptions = [
    { value: 0, label: "On/Off" },
    { value: 1, label: "Analog" },
  ];

  const tempTypeOptions = [
    { value: 0, label: "Thermostat" },
    { value: 1, label: "RCU" },
  ];

  const tempUnitOptions = [
    { value: 0, label: "°C" },
    { value: 1, label: "°F" },
  ];

  const valveContactOptions = [
    { value: 0, label: "NO" },
    { value: 1, label: "NC" },
  ];

  const valveTypeOptions = [
    { value: 0, label: "Type 1" },
    { value: 1, label: "Type 2" },
  ];

  const deadBandOptions = [
    { value: 0, label: "0.5°C" },
    { value: 1, label: "1.0°C" },
    { value: 2, label: "1.5°C" },
    { value: 3, label: "2.0°C" },
    { value: 4, label: "2.5°C" },
    { value: 5, label: "3.0°C" },
  ];

  const windowsOptions = [
    { value: 0, label: "Normal" },
    { value: 1, label: "Bypass" },
  ];

  // Generate group options (0-255)
  const generateGroupOptions = () => {
    const options = [{ value: 0, label: "Unused" }];
    for (let i = 1; i <= 255; i++) {
      options.push({ value: i, label: `Group ${i}` });
    }
    return options;
  };

  const groupOptions = generateGroupOptions();

  return (
    <Card>
      <Collapsible open={isOpen} onOpenChange={setIsOpen}>
        <CollapsibleTrigger asChild>
          <CardHeader className="cursor-pointer hover:bg-muted/50">
            <CardTitle className="flex items-center justify-between">
              <span>AC Output {outputIndex + 1}</span>
              <div className="flex items-center gap-2">
                <span
                  className={`text-sm ${
                    localConfig.enable
                      ? "text-green-600"
                      : "text-muted-foreground"
                  }`}
                >
                  {localConfig.enable ? "Enabled" : "Disabled"}
                </span>
                {isOpen ? (
                  <ChevronDown className="h-4 w-4" />
                ) : (
                  <ChevronRight className="h-4 w-4" />
                )}
              </div>
            </CardTitle>
          </CardHeader>
        </CollapsibleTrigger>

        <CollapsibleContent>
          <CardContent className="space-y-6">
            {/* Enable Checkbox */}
            <div className="flex items-center space-x-2">
              <Checkbox
                id={`enable-${outputIndex}`}
                checked={localConfig.enable}
                onCheckedChange={(checked) => handleChange("enable", checked)}
              />
              <Label htmlFor={`enable-${outputIndex}`}>Enable AC Output</Label>
            </div>

            {/* Basic Configuration */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label>Windows Mode</Label>
                <Select
                  value={localConfig.windows_mode.toString()}
                  onValueChange={(value) =>
                    handleChange("windows_mode", parseInt(value))
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {windowsModeOptions.map((option) => (
                      <SelectItem
                        key={option.value}
                        value={option.value.toString()}
                      >
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label>Fan Type</Label>
                <Select
                  value={localConfig.fan_type.toString()}
                  onValueChange={(value) =>
                    handleChange("fan_type", parseInt(value))
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {fanTypeOptions.map((option) => (
                      <SelectItem
                        key={option.value}
                        value={option.value.toString()}
                      >
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label>Temperature Type</Label>
                <Select
                  value={localConfig.temp_type.toString()}
                  onValueChange={(value) =>
                    handleChange("temp_type", parseInt(value))
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {tempTypeOptions.map((option) => (
                      <SelectItem
                        key={option.value}
                        value={option.value.toString()}
                      >
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label>Temperature Unit</Label>
                <Select
                  value={localConfig.temp_unit.toString()}
                  onValueChange={(value) =>
                    handleChange("temp_unit", parseInt(value))
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {tempUnitOptions.map((option) => (
                      <SelectItem
                        key={option.value}
                        value={option.value.toString()}
                      >
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label>Valve Contact</Label>
                <Select
                  value={localConfig.valve_contact.toString()}
                  onValueChange={(value) =>
                    handleChange("valve_contact", parseInt(value))
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {valveContactOptions.map((option) => (
                      <SelectItem
                        key={option.value}
                        value={option.value.toString()}
                      >
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label>Dead Band</Label>
                <Select
                  value={localConfig.dead_band.toString()}
                  onValueChange={(value) =>
                    handleChange("dead_band", parseInt(value))
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {deadBandOptions.map((option) => (
                      <SelectItem
                        key={option.value}
                        value={option.value.toString()}
                      >
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label>Windows</Label>
                <Select
                  value={localConfig.windows.toString()}
                  onValueChange={(value) =>
                    handleChange("windows", parseInt(value))
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {windowsOptions.map((option) => (
                      <SelectItem
                        key={option.value}
                        value={option.value.toString()}
                      >
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Fan Group Configuration */}
            <div>
              <Label className="text-base font-semibold">Fan Groups</Label>
              <div className="grid grid-cols-2 gap-4 mt-2">
                <div>
                  <Label>Low Fan</Label>
                  <Select
                    value={localConfig.group_low_fan.toString()}
                    onValueChange={(value) =>
                      handleChange("group_low_fan", parseInt(value))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {groupOptions.map((option) => (
                        <SelectItem
                          key={option.value}
                          value={option.value.toString()}
                        >
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label>Med Fan</Label>
                  <Select
                    value={localConfig.group_med_fan.toString()}
                    onValueChange={(value) =>
                      handleChange("group_med_fan", parseInt(value))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {groupOptions.map((option) => (
                        <SelectItem
                          key={option.value}
                          value={option.value.toString()}
                        >
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label>High Fan</Label>
                  <Select
                    value={localConfig.group_high_fan.toString()}
                    onValueChange={(value) =>
                      handleChange("group_high_fan", parseInt(value))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {groupOptions.map((option) => (
                        <SelectItem
                          key={option.value}
                          value={option.value.toString()}
                        >
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label>Analog Fan</Label>
                  <Select
                    value={localConfig.group_analog_fan.toString()}
                    onValueChange={(value) =>
                      handleChange("group_analog_fan", parseInt(value))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {groupOptions.map((option) => (
                        <SelectItem
                          key={option.value}
                          value={option.value.toString()}
                        >
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>

            {/* Cooling/Heating Group Configuration */}
            <div>
              <Label className="text-base font-semibold">
                Cooling/Heating Groups
              </Label>
              <div className="grid grid-cols-2 gap-4 mt-2">
                <div>
                  <Label>Analog Cool</Label>
                  <Select
                    value={localConfig.group_analog_cool.toString()}
                    onValueChange={(value) =>
                      handleChange("group_analog_cool", parseInt(value))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {groupOptions.map((option) => (
                        <SelectItem
                          key={option.value}
                          value={option.value.toString()}
                        >
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label>Analog Heat</Label>
                  <Select
                    value={localConfig.group_analog_heat.toString()}
                    onValueChange={(value) =>
                      handleChange("group_analog_heat", parseInt(value))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {groupOptions.map((option) => (
                        <SelectItem
                          key={option.value}
                          value={option.value.toString()}
                        >
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label>Cool Open</Label>
                  <Select
                    value={localConfig.group_cool_open.toString()}
                    onValueChange={(value) =>
                      handleChange("group_cool_open", parseInt(value))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {groupOptions.map((option) => (
                        <SelectItem
                          key={option.value}
                          value={option.value.toString()}
                        >
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label>Cool Close</Label>
                  <Select
                    value={localConfig.group_cool_close.toString()}
                    onValueChange={(value) =>
                      handleChange("group_cool_close", parseInt(value))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {groupOptions.map((option) => (
                        <SelectItem
                          key={option.value}
                          value={option.value.toString()}
                        >
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label>Heat Open</Label>
                  <Select
                    value={localConfig.group_heat_open.toString()}
                    onValueChange={(value) =>
                      handleChange("group_heat_open", parseInt(value))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {groupOptions.map((option) => (
                        <SelectItem
                          key={option.value}
                          value={option.value.toString()}
                        >
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label>Heat Close</Label>
                  <Select
                    value={localConfig.group_heat_close.toString()}
                    onValueChange={(value) =>
                      handleChange("group_heat_close", parseInt(value))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {groupOptions.map((option) => (
                        <SelectItem
                          key={option.value}
                          value={option.value.toString()}
                        >
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>
          </CardContent>
        </CollapsibleContent>
      </Collapsible>
    </Card>
  );
};

ACOutputConfig.displayName = "ACOutputConfig";

export default ACOutputConfig;
