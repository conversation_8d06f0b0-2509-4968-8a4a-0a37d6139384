# Phân tích so s<PERSON>h chức năng I/O Configuration

## **Ứng dụng gốc RLC (WinForms)**

### **C<PERSON>u trúc dữ liệu chính:**

#### **1. IOProperty Structure (Input Configuration)**
```csharp
public struct IOProperty
{
    public int Input;           // Input index
    public int Function;        // Input function type
    public int Ramp;           // Ramp time (seconds)
    public int Preset;         // Preset value (0-255)
    public int Led_Status;     // LED display status (bits for different modes)
    public int Auto_Mode;      // Auto mode setting
    public int Auto_Time;      // Auto time setting
    public int DelayOff;       // Delay off time (seconds)
    public int DelayOn;        // Delay on time (seconds)
    public int NumGroup;       // Number of groups assigned
    public int[] Group;        // Array of group IDs
    public byte[] Preset_Group; // Array of preset values for each group
}
```

#### **2. AC Output Configuration Structure**
```csharp
public struct ac_out_cfg_t
{
    public Byte group;
    public Byte enable;
    public Byte windows_mode;
    public Byte fan_type;
    public Byte temp_type;
    public Byte temp_unit;
    public Byte valve_contact;
    public Byte valve_type;
    public Byte dead_band;
    public Byte group_low_fan;
    public Byte group_med_fan;
    public Byte group_high_fan;
    public Byte group_analog_fan;
    public Byte group_analog_cool;
    public Byte group_analog_heat;
    public Byte group_cool_open;
    public Byte group_cool_close;
    public Byte group_heat_open;
    public Byte group_heat_close;
    public Byte windows;
    // Additional fields for power modes and temperature points
    public Byte unoccupyPower;
    public Byte occupyPower;
    public Byte standbyPower;
    public Int16 unoccupied_cool_point;
    public Int16 occupied_cool_point;
    public Int16 standby_cool_point;
    // ... more fields
}
```

#### **3. Light Output Configuration Structure**
```csharp
public struct output_info2_t
{
    public Byte index;
    public Byte min_dim;
    public Byte max_dim;
    public Byte auto_trigger;
    public Byte schedule_on_hour;
    public Byte schedule_on_min;
    public Byte schedule_off_hour;
    public Byte schedule_off_min;
}
```

### **Chức năng chính của ứng dụng gốc:**

#### **1. Input Configuration (ConfigIO)**
- **Ramp Configuration**: Thời gian tăng/giảm độ sáng (0-3600 giây)
- **Preset Configuration**: Giá trị preset (0-255, có thể hiển thị % hoặc raw value)
- **LED Status Configuration**: 
  - Display modes: OFF, ON, ON/OFF, 2-colors
  - Nightlight enable/disable
  - Backlight enable/disable
- **Delay Off Configuration**: Thời gian delay tắt (giờ:phút:giây)
- **Multi-Group Support**: 
  - Active groups và Inactive groups (cho keycard functions)
  - Tối đa 60 groups (30 cho mỗi loại nếu có keycard)
  - Group management với preset values
- **Function-based Configuration**: Các tùy chọn khác nhau dựa trên input function

#### **2. Output Configuration**
- **Light Output**: Delay on/off, dimming min/max, scheduling
- **AC Output**: Comprehensive HVAC control với nhiều parameters

#### **3. Group Management**
- Dynamic group assignment
- Group creation và editing
- Function-specific group types
- Preset values per group

## **Triển khai hiện tại (Electron App)**

### **Cấu trúc database:**

#### **1. unit_io_config table**
```sql
CREATE TABLE unit_io_config (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    unit_id INTEGER NOT NULL,
    input_count INTEGER DEFAULT 0,
    lighting_output_count INTEGER DEFAULT 0,
    ac_output_count INTEGER DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
)
```

#### **2. ac_output_config table**
```sql
CREATE TABLE ac_output_config (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    unit_id INTEGER NOT NULL,
    output_index INTEGER NOT NULL,
    enable BOOLEAN DEFAULT 0,
    windows_mode INTEGER DEFAULT 0,
    fan_type INTEGER DEFAULT 0,
    temp_type INTEGER DEFAULT 0,
    temp_unit INTEGER DEFAULT 0,
    valve_contact INTEGER DEFAULT 0,
    valve_type INTEGER DEFAULT 0,
    dead_band INTEGER DEFAULT 0,
    group_low_fan INTEGER DEFAULT 0,
    group_med_fan INTEGER DEFAULT 0,
    group_high_fan INTEGER DEFAULT 0,
    group_analog_fan INTEGER DEFAULT 0,
    group_analog_cool INTEGER DEFAULT 0,
    group_analog_heat INTEGER DEFAULT 0,
    group_cool_open INTEGER DEFAULT 0,
    group_cool_close INTEGER DEFAULT 0,
    group_heat_open INTEGER DEFAULT 0,
    group_heat_close INTEGER DEFAULT 0,
    windows INTEGER DEFAULT 0
)
```

#### **3. light_output_config table**
```sql
CREATE TABLE light_output_config (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    unit_id INTEGER NOT NULL,
    output_index INTEGER NOT NULL,
    group_id INTEGER DEFAULT 0,
    delay_on INTEGER DEFAULT 0,
    delay_off INTEGER DEFAULT 0,
    min_dim INTEGER DEFAULT 3,
    max_dim INTEGER DEFAULT 255,
    auto_trigger BOOLEAN DEFAULT 0,
    schedule_on_hour INTEGER DEFAULT 0,
    schedule_on_min INTEGER DEFAULT 0,
    schedule_off_hour INTEGER DEFAULT 0,
    schedule_off_min INTEGER DEFAULT 0
)
```

### **Chức năng đã triển khai:**

#### **1. I/O Count Configuration**
- Input count, lighting output count, AC output count
- Default values based on unit type

#### **2. Light Output Configuration**
- Group assignment (0-255)
- Delay on/off configuration (hours:minutes:seconds)
- Dimming configuration (min/max percentages)
- Auto trigger và scheduling
- Collapsible UI cho từng output

#### **3. AC Output Configuration**
- Basic AC parameters
- Group assignments cho các chức năng khác nhau
- Enable/disable options

## **So sánh và phân tích thiếu sót**

### **❌ Thiếu hoàn toàn - Input Configuration**

Ứng dụng hiện tại **KHÔNG CÓ** input configuration, đây là thiếu sót lớn nhất:

1. **Không có bảng database cho input config**
2. **Không có UI cho input configuration**
3. **Không có các chức năng:**
   - Function assignment cho inputs
   - Ramp configuration
   - Preset configuration
   - LED status configuration
   - Multi-group assignment
   - Delay off configuration

### **⚠️ Triển khai không đầy đủ**

#### **1. AC Output Configuration**
- **Thiếu**: Power mode settings (unoccupy/occupy/standby)
- **Thiếu**: Temperature setpoints
- **Thiếu**: Reserved fields cho future expansion

#### **2. Group Management**
- **Thiếu**: Dynamic group creation/editing
- **Thiếu**: Function-specific group types
- **Thiếu**: Group preset values
- **Thiếu**: Multi-group support (active/inactive)

#### **3. UI/UX**
- **Thiếu**: Function-based conditional UI
- **Thiếu**: Group management interface
- **Thiếu**: Input configuration interface
- **Thiếu**: Advanced AC configuration options

### **✅ Đã triển khai tốt**

1. **Database structure** cho output configurations
2. **Basic light output configuration** với UI tốt
3. **Collapsible interface** cho multiple outputs
4. **Time configuration** với hours:minutes:seconds
5. **Percentage-based dimming** configuration

## **Kết luận**

Triển khai hiện tại chỉ bao gồm khoảng **30-40%** chức năng của ứng dụng gốc. Thiếu sót lớn nhất là **hoàn toàn không có input configuration**, đây là phần cốt lõi của I/O configuration trong ứng dụng gốc.

Để đạt được tính năng tương đương với ứng dụng gốc, cần:

1. **Thêm input configuration** (ưu tiên cao nhất)
2. **Mở rộng AC output configuration**
3. **Thêm group management system**
4. **Cải thiện UI để support function-based configuration**
