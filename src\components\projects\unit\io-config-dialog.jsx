import React, { useState, useEffect, useCallback } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  DialogHeader,
  <PERSON>alogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";
import ACOutputConfig from "./ac-output-config";
import LightOutputConfig from "./light-output-config";

const IOConfigDialog = ({ open, onOpenChange, unit, onSave }) => {
  const [ioConfig, setIOConfig] = useState({
    input_count: 0,
    lighting_output_count: 0,
    ac_output_count: 0,
  });
  const [acConfigs, setACConfigs] = useState([]);
  const [lightConfigs, setLightConfigs] = useState([]);
  const [loading, setLoading] = useState(false);

  // Load I/O configuration when dialog opens
  useEffect(() => {
    if (open && unit?.id) {
      loadIOConfig();
    }
  }, [open, unit?.id]);

  const loadIOConfig = useCallback(async () => {
    if (!unit?.id) {
      console.warn("loadIOConfig called without unit.id");
      return;
    }

    setLoading(true);
    try {
      // Load I/O config
      const ioConfigResponse =
        await window.electronAPI.database.getUnitIOConfig(unit.id);
      if (ioConfigResponse) {
        setIOConfig(ioConfigResponse);

        // Load AC output configs
        const acConfigsResponse =
          await window.electronAPI.database.getACOutputConfigs(unit.id);
        setACConfigs(acConfigsResponse || []);

        // Load Light output configs
        const lightConfigsResponse =
          await window.electronAPI.database.getLightOutputConfigs(unit.id);
        setLightConfigs(lightConfigsResponse || []);
      } else {
        // Set default values based on unit type
        const defaultConfig = getDefaultIOConfig(unit?.type);
        setIOConfig(defaultConfig);
        setACConfigs([]);
        setLightConfigs([]);
      }
    } catch (error) {
      console.error("Failed to load I/O configuration:", error);
      // Set default values on error
      const defaultConfig = getDefaultIOConfig(unit?.type);
      setIOConfig(defaultConfig);
      setACConfigs([]);
      setLightConfigs([]);
    } finally {
      setLoading(false);
    }
  }, [unit?.id, unit?.type]);

  const getDefaultIOConfig = (unitType) => {
    // Default I/O counts based on unit type (similar to WinForms app)
    const defaults = {
      "Room Logic Controller": {
        input_count: 48,
        lighting_output_count: 16,
        ac_output_count: 4,
      },
      "Bedside-17T": {
        input_count: 17,
        lighting_output_count: 8,
        ac_output_count: 2,
      },
      "RCU-48IN-16RL": {
        input_count: 48,
        lighting_output_count: 16,
        ac_output_count: 0,
      },
      "RCU-21IN-10RL": {
        input_count: 21,
        lighting_output_count: 10,
        ac_output_count: 0,
      },
      "RCU-30IN-10RL": {
        input_count: 30,
        lighting_output_count: 10,
        ac_output_count: 0,
      },
    };

    return (
      defaults[unitType] || {
        input_count: 0,
        lighting_output_count: 0,
        ac_output_count: 0,
      }
    );
  };

  const handleIOConfigChange = (field, value) => {
    setIOConfig((prev) => ({
      ...prev,
      [field]: parseInt(value) || 0,
    }));
  };

  const handleACConfigChange = (outputIndex, config) => {
    setACConfigs((prev) => {
      const newConfigs = [...prev];
      const existingIndex = newConfigs.findIndex(
        (c) => c.output_index === outputIndex
      );

      if (existingIndex >= 0) {
        newConfigs[existingIndex] = { ...newConfigs[existingIndex], ...config };
      } else {
        newConfigs.push({ output_index: outputIndex, ...config });
      }

      return newConfigs;
    });
  };

  const handleLightConfigChange = (outputIndex, config) => {
    setLightConfigs((prev) => {
      const newConfigs = [...prev];
      const existingIndex = newConfigs.findIndex(
        (c) => c.output_index === outputIndex
      );

      if (existingIndex >= 0) {
        newConfigs[existingIndex] = { ...newConfigs[existingIndex], ...config };
      } else {
        newConfigs.push({ output_index: outputIndex, ...config });
      }

      return newConfigs;
    });
  };

  const handleSave = useCallback(async () => {
    if (!unit?.id) return;

    setLoading(true);
    try {
      // Save I/O config
      await window.electronAPI.database.createOrUpdateUnitIOConfig(
        unit.id,
        ioConfig
      );

      // Save AC output configs
      for (const acConfig of acConfigs) {
        await window.electronAPI.database.createOrUpdateACOutputConfig(
          unit.id,
          acConfig.output_index,
          acConfig
        );
      }

      // Save Light output configs
      for (const lightConfig of lightConfigs) {
        await window.electronAPI.database.createOrUpdateLightOutputConfig(
          unit.id,
          lightConfig.output_index,
          lightConfig
        );
      }

      onSave?.();
      onOpenChange(false);
    } catch (error) {
      console.error("Failed to save I/O configuration:", error);
    } finally {
      setLoading(false);
    }
  }, [unit?.id, ioConfig, acConfigs, lightConfigs, onSave, onOpenChange]);

  const renderACOutputs = () => {
    if (!ioConfig || typeof ioConfig.ac_output_count !== "number") {
      return null;
    }

    const outputs = [];
    for (let i = 0; i < ioConfig.ac_output_count; i++) {
      const config = acConfigs.find((c) => c.output_index === i) || {};
      outputs.push(
        <ACOutputConfig
          key={`ac-output-${i}`}
          outputIndex={i}
          config={config}
          onChange={(config) => handleACConfigChange(i, config)}
        />
      );
    }
    return outputs;
  };

  const renderLightOutputs = () => {
    if (!ioConfig || typeof ioConfig.lighting_output_count !== "number") {
      return null;
    }

    const outputs = [];
    for (let i = 0; i < ioConfig.lighting_output_count; i++) {
      const config = lightConfigs.find((c) => c.output_index === i) || {};
      outputs.push(
        <LightOutputConfig
          key={`light-output-${i}`}
          outputIndex={i}
          config={config}
          onChange={(config) => handleLightConfigChange(i, config)}
        />
      );
    }
    return outputs;
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh]">
        <DialogHeader>
          <DialogTitle>I/O Configuration - {unit?.type}</DialogTitle>
        </DialogHeader>

        <ScrollArea className="flex-1 pr-4">
          <div className="space-y-6">
            {loading ? (
              <div className="text-center py-8">
                <div className="text-muted-foreground">
                  Loading I/O configuration...
                </div>
              </div>
            ) : (
              <>
                {/* I/O Count Configuration */}
                <Card>
                  <CardHeader>
                    <CardTitle>I/O Count Configuration</CardTitle>
                  </CardHeader>
                  <CardContent className="grid grid-cols-3 gap-4">
                    <div>
                      <Label htmlFor="input_count">Input Count</Label>
                      <Input
                        id="input_count"
                        type="number"
                        min="0"
                        max="255"
                        value={ioConfig.input_count || 0}
                        onChange={(e) =>
                          handleIOConfigChange("input_count", e.target.value)
                        }
                      />
                    </div>
                    <div>
                      <Label htmlFor="lighting_output_count">
                        Lighting Output Count
                      </Label>
                      <Input
                        id="lighting_output_count"
                        type="number"
                        min="0"
                        max="255"
                        value={ioConfig.lighting_output_count || 0}
                        onChange={(e) =>
                          handleIOConfigChange(
                            "lighting_output_count",
                            e.target.value
                          )
                        }
                      />
                    </div>
                    <div>
                      <Label htmlFor="ac_output_count">AC Output Count</Label>
                      <Input
                        id="ac_output_count"
                        type="number"
                        min="0"
                        max="255"
                        value={ioConfig.ac_output_count || 0}
                        onChange={(e) =>
                          handleIOConfigChange(
                            "ac_output_count",
                            e.target.value
                          )
                        }
                      />
                    </div>
                  </CardContent>
                </Card>

                {/* Output Configuration Tabs */}
                <Tabs defaultValue="lighting" className="w-full">
                  <TabsList className="grid w-full grid-cols-2">
                    <TabsTrigger value="lighting">
                      Lighting Outputs ({ioConfig.lighting_output_count || 0})
                    </TabsTrigger>
                    <TabsTrigger value="ac">
                      AC Outputs ({ioConfig.ac_output_count || 0})
                    </TabsTrigger>
                  </TabsList>

                  <TabsContent value="lighting" className="space-y-4">
                    {(ioConfig.lighting_output_count || 0) > 0 ? (
                      renderLightOutputs()
                    ) : (
                      <div className="text-center text-muted-foreground py-8">
                        No lighting outputs configured
                      </div>
                    )}
                  </TabsContent>

                  <TabsContent value="ac" className="space-y-4">
                    {(ioConfig.ac_output_count || 0) > 0 ? (
                      renderACOutputs()
                    ) : (
                      <div className="text-center text-muted-foreground py-8">
                        No AC outputs configured
                      </div>
                    )}
                  </TabsContent>
                </Tabs>
              </>
            )}
          </div>
        </ScrollArea>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={loading}
          >
            Cancel
          </Button>
          <Button onClick={handleSave} disabled={loading}>
            {loading ? "Saving..." : "Save Configuration"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

IOConfigDialog.displayName = "IOConfigDialog";

export default IOConfigDialog;
