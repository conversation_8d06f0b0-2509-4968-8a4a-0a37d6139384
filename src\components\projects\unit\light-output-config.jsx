import React, { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import { ChevronDown, ChevronRight } from "lucide-react";

const LightOutputConfig = ({ outputIndex, config, onChange }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [localConfig, setLocalConfig] = useState({
    group_id: 0,
    delay_on: 0,
    delay_off: 0,
    min_dim: 3,
    max_dim: 255,
    auto_trigger: false,
    schedule_on_hour: 0,
    schedule_on_min: 0,
    schedule_off_hour: 0,
    schedule_off_min: 0,
    ...config,
  });

  useEffect(() => {
    setLocalConfig((prev) => ({ ...prev, ...config }));
  }, [config]);

  const handleChange = (field, value) => {
    const newConfig = { ...localConfig, [field]: value };
    setLocalConfig(newConfig);
    onChange(newConfig);
  };

  // Convert seconds to hours, minutes, seconds
  const convertSecondsToTime = (totalSeconds) => {
    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const seconds = totalSeconds % 60;
    return { hours, minutes, seconds };
  };

  // Convert hours, minutes, seconds to total seconds
  const convertTimeToSeconds = (hours, minutes, seconds) => {
    return hours * 3600 + minutes * 60 + seconds;
  };

  const delayOnTime = convertSecondsToTime(localConfig.delay_on);
  const delayOffTime = convertSecondsToTime(localConfig.delay_off);

  const handleDelayOnChange = (field, value) => {
    const newTime = { ...delayOnTime, [field]: parseInt(value) || 0 };
    const totalSeconds = convertTimeToSeconds(
      newTime.hours,
      newTime.minutes,
      newTime.seconds
    );
    handleChange("delay_on", totalSeconds);
  };

  const handleDelayOffChange = (field, value) => {
    const newTime = { ...delayOffTime, [field]: parseInt(value) || 0 };
    const totalSeconds = convertTimeToSeconds(
      newTime.hours,
      newTime.minutes,
      newTime.seconds
    );
    handleChange("delay_off", totalSeconds);
  };

  // Generate group options (0-255)
  const generateGroupOptions = () => {
    const options = [{ value: 0, label: "Unused" }];
    for (let i = 1; i <= 255; i++) {
      options.push({ value: i, label: `Group ${i}` });
    }
    return options;
  };

  // Generate hour options (0-18)
  const generateHourOptions = () => {
    const options = [];
    for (let i = 0; i <= 18; i++) {
      options.push({ value: i, label: i.toString() });
    }
    return options;
  };

  // Generate minute options (0-59, but limited to 11 if hour is 18)
  const generateMinuteOptions = (hour) => {
    const options = [];
    const maxMinutes = hour === 18 ? 11 : 59;
    for (let i = 0; i <= maxMinutes; i++) {
      options.push({ value: i, label: i.toString() });
    }
    return options;
  };

  // Generate second options (0-59)
  const generateSecondOptions = () => {
    const options = [];
    for (let i = 0; i <= 59; i++) {
      options.push({ value: i, label: i.toString() });
    }
    return options;
  };

  // Generate dimming percentage options (1-30% for min, 70-100% for max)
  const generateMinDimOptions = () => {
    const options = [];
    for (let i = 1; i <= 30; i++) {
      options.push({ value: i, label: `${i}%` });
    }
    return options;
  };

  const generateMaxDimOptions = () => {
    const options = [];
    for (let i = 70; i <= 100; i++) {
      options.push({ value: i, label: `${i}%` });
    }
    return options;
  };

  const groupOptions = generateGroupOptions();
  const hourOptions = generateHourOptions();
  const secondOptions = generateSecondOptions();
  const minDimOptions = generateMinDimOptions();
  const maxDimOptions = generateMaxDimOptions();

  // Convert dimming value from 0-255 to percentage
  const dimToPercentage = (dimValue) => Math.round((dimValue * 100) / 255);
  const percentageToDim = (percentage) => Math.round((percentage * 255) / 100);

  const minDimPercentage = dimToPercentage(localConfig.min_dim);
  const maxDimPercentage = dimToPercentage(localConfig.max_dim);

  return (
    <Card>
      <Collapsible open={isOpen} onOpenChange={setIsOpen}>
        <CollapsibleTrigger asChild>
          <CardHeader className="cursor-pointer hover:bg-muted/50">
            <CardTitle className="flex items-center justify-between">
              <span>Light Output {outputIndex + 1}</span>
              <div className="flex items-center gap-2">
                <span
                  className={`text-sm ${
                    localConfig.group_id > 0
                      ? "text-green-600"
                      : "text-muted-foreground"
                  }`}
                >
                  {localConfig.group_id > 0
                    ? `Group ${localConfig.group_id}`
                    : "Unused"}
                </span>
                {isOpen ? (
                  <ChevronDown className="h-4 w-4" />
                ) : (
                  <ChevronRight className="h-4 w-4" />
                )}
              </div>
            </CardTitle>
          </CardHeader>
        </CollapsibleTrigger>

        <CollapsibleContent>
          <CardContent className="space-y-6">
            {/* Group Assignment */}
            <div>
              <Label>Lighting Group</Label>
              <Select
                value={localConfig.group_id.toString()}
                onValueChange={(value) =>
                  handleChange("group_id", parseInt(value))
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {groupOptions.map((option) => (
                    <SelectItem
                      key={option.value}
                      value={option.value.toString()}
                    >
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Delay Configuration */}
            <div>
              <Label className="text-base font-semibold">
                Delay Configuration
              </Label>
              <div className="grid grid-cols-2 gap-4 mt-2">
                <div>
                  <Label>Delay On Output</Label>
                  <div className="flex gap-2 items-center">
                    <Select
                      value={delayOnTime.hours.toString()}
                      onValueChange={(value) =>
                        handleDelayOnChange("hours", value)
                      }
                    >
                      <SelectTrigger className="w-20">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {hourOptions.map((option) => (
                          <SelectItem
                            key={option.value}
                            value={option.value.toString()}
                          >
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <span className="text-sm">h</span>

                    <Select
                      value={delayOnTime.minutes.toString()}
                      onValueChange={(value) =>
                        handleDelayOnChange("minutes", value)
                      }
                    >
                      <SelectTrigger className="w-20">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {generateMinuteOptions(delayOnTime.hours).map(
                          (option) => (
                            <SelectItem
                              key={option.value}
                              value={option.value.toString()}
                            >
                              {option.label}
                            </SelectItem>
                          )
                        )}
                      </SelectContent>
                    </Select>
                    <span className="text-sm">m</span>

                    <Select
                      value={delayOnTime.seconds.toString()}
                      onValueChange={(value) =>
                        handleDelayOnChange("seconds", value)
                      }
                    >
                      <SelectTrigger className="w-20">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {secondOptions.map((option) => (
                          <SelectItem
                            key={option.value}
                            value={option.value.toString()}
                          >
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <span className="text-sm">s</span>
                  </div>
                </div>

                <div>
                  <Label>Delay Off Output</Label>
                  <div className="flex gap-2 items-center">
                    <Select
                      value={delayOffTime.hours.toString()}
                      onValueChange={(value) =>
                        handleDelayOffChange("hours", value)
                      }
                    >
                      <SelectTrigger className="w-20">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {hourOptions.map((option) => (
                          <SelectItem
                            key={option.value}
                            value={option.value.toString()}
                          >
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <span className="text-sm">h</span>

                    <Select
                      value={delayOffTime.minutes.toString()}
                      onValueChange={(value) =>
                        handleDelayOffChange("minutes", value)
                      }
                    >
                      <SelectTrigger className="w-20">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {generateMinuteOptions(delayOffTime.hours).map(
                          (option) => (
                            <SelectItem
                              key={option.value}
                              value={option.value.toString()}
                            >
                              {option.label}
                            </SelectItem>
                          )
                        )}
                      </SelectContent>
                    </Select>
                    <span className="text-sm">m</span>

                    <Select
                      value={delayOffTime.seconds.toString()}
                      onValueChange={(value) =>
                        handleDelayOffChange("seconds", value)
                      }
                    >
                      <SelectTrigger className="w-20">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {secondOptions.map((option) => (
                          <SelectItem
                            key={option.value}
                            value={option.value.toString()}
                          >
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <span className="text-sm">s</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Dimming Configuration */}
            <div>
              <Label className="text-base font-semibold">
                Dimming Configuration
              </Label>
              <div className="grid grid-cols-2 gap-4 mt-2">
                <div>
                  <Label>Min Dim</Label>
                  <Select
                    value={minDimPercentage.toString()}
                    onValueChange={(value) =>
                      handleChange("min_dim", percentageToDim(parseInt(value)))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {minDimOptions.map((option) => (
                        <SelectItem
                          key={option.value}
                          value={option.value.toString()}
                        >
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label>Max Dim</Label>
                  <Select
                    value={maxDimPercentage.toString()}
                    onValueChange={(value) =>
                      handleChange("max_dim", percentageToDim(parseInt(value)))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {maxDimOptions.map((option) => (
                        <SelectItem
                          key={option.value}
                          value={option.value.toString()}
                        >
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>

            {/* Auto Trigger and Scheduling */}
            <div>
              <div className="flex items-center space-x-2 mb-4">
                <Checkbox
                  id={`auto-trigger-${outputIndex}`}
                  checked={localConfig.auto_trigger}
                  onCheckedChange={(checked) =>
                    handleChange("auto_trigger", checked)
                  }
                />
                <Label htmlFor={`auto-trigger-${outputIndex}`}>
                  Auto Trigger
                </Label>
              </div>

              {localConfig.auto_trigger && (
                <div>
                  <Label className="text-base font-semibold">
                    Schedule Configuration
                  </Label>
                  <div className="grid grid-cols-2 gap-4 mt-2">
                    <div>
                      <Label>Schedule On</Label>
                      <div className="flex gap-2 items-center">
                        <Select
                          value={localConfig.schedule_on_hour.toString()}
                          onValueChange={(value) =>
                            handleChange("schedule_on_hour", parseInt(value))
                          }
                        >
                          <SelectTrigger className="w-20">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {hourOptions.map((option) => (
                              <SelectItem
                                key={option.value}
                                value={option.value.toString()}
                              >
                                {option.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <span className="text-sm">h</span>

                        <Select
                          value={localConfig.schedule_on_min.toString()}
                          onValueChange={(value) =>
                            handleChange("schedule_on_min", parseInt(value))
                          }
                        >
                          <SelectTrigger className="w-20">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {generateMinuteOptions(
                              localConfig.schedule_on_hour
                            ).map((option) => (
                              <SelectItem
                                key={option.value}
                                value={option.value.toString()}
                              >
                                {option.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <span className="text-sm">m</span>
                      </div>
                    </div>

                    <div>
                      <Label>Schedule Off</Label>
                      <div className="flex gap-2 items-center">
                        <Select
                          value={localConfig.schedule_off_hour.toString()}
                          onValueChange={(value) =>
                            handleChange("schedule_off_hour", parseInt(value))
                          }
                        >
                          <SelectTrigger className="w-20">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {hourOptions.map((option) => (
                              <SelectItem
                                key={option.value}
                                value={option.value.toString()}
                              >
                                {option.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <span className="text-sm">h</span>

                        <Select
                          value={localConfig.schedule_off_min.toString()}
                          onValueChange={(value) =>
                            handleChange("schedule_off_min", parseInt(value))
                          }
                        >
                          <SelectTrigger className="w-20">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {generateMinuteOptions(
                              localConfig.schedule_off_hour
                            ).map((option) => (
                              <SelectItem
                                key={option.value}
                                value={option.value.toString()}
                              >
                                {option.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <span className="text-sm">m</span>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </CollapsibleContent>
      </Collapsible>
    </Card>
  );
};

LightOutputConfig.displayName = "LightOutputConfig";

export default LightOutputConfig;
